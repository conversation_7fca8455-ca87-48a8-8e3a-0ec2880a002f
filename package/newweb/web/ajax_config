#!/bin/sh
#This script is created by ssparser automatically. The parser first created by <PERSON><PERSON><PERSON><PERSON>

# 立即输出 HTTP 头部，防止被其他脚本污染
printf "Content-type: application/json;charset=gb2312\r\n"
printf "Cache-Control: no-cache\r\n"
printf "\r\n"

# 配置文件路径
PGPATH="${PGPATH:-/usr/panabit}"
CONFIG_FILE="${PGPATH}/app/unisase_agent/config/config.yaml"

# 简化的安全检查，避免调用可能产生HTML输出的函数
if [ -z "$REQUEST_METHOD" ]; then
    echo '{"error": "Invalid request method"}'
    exit 1
fi

# 错误响应函数 - 简化版本，避免调用可能产生HTML的函数
error_response() {
    local message="$1"
    local code="${2:-500}"
    echo "{\"error\": \"$message\", \"code\": $code}"
    exit 1
}

# 成功响应函数 - 简化版本
success_response() {
    local message="$1"
    echo "{\"success\": true, \"message\": \"$message\"}"
}

# 完整的YAML转JSON函数（支持TLS配置）
yaml_to_json() {
    local yaml_file="$1"

    # 检查文件是否存在
    if [ ! -f "$yaml_file" ]; then
        error_response "配置文件不存在" 404
    fi

    # 提取客户端配置
    local customer_id=$(grep "customer-id:" "$yaml_file" | sed 's/.*customer-id:[[:space:]]*//')
    local client_id=$(grep "client-id:" "$yaml_file" | sed 's/.*client-id:[[:space:]]*//')

    # 提取TLS配置 - 使用更安全的方法
    local tls_enabled=$(grep -A 10 "tls:" "$yaml_file" | grep "enabled:" | head -1 | sed 's/.*enabled:[[:space:]]*//' | tr -d '"' | tr -d "'")
    local tls_cert_dir=$(grep -A 10 "tls:" "$yaml_file" | grep "cert-dir:" | head -1 | sed 's/.*cert-dir:[[:space:]]*//' | tr -d '"' | tr -d "'")
    local tls_cert_file=$(grep -A 10 "tls:" "$yaml_file" | grep "cert-file:" | head -1 | sed 's/.*cert-file:[[:space:]]*//' | tr -d '"' | tr -d "'")
    local tls_key_file=$(grep -A 10 "tls:" "$yaml_file" | grep "key-file:" | head -1 | sed 's/.*key-file:[[:space:]]*//' | tr -d '"' | tr -d "'")
    local tls_skip_verify=$(grep -A 10 "tls:" "$yaml_file" | grep "skip-verify:" | head -1 | sed 's/.*skip-verify:[[:space:]]*//' | tr -d '"' | tr -d "'")
    local tls_auto_generate=$(grep -A 10 "tls:" "$yaml_file" | grep "auto-generate:" | head -1 | sed 's/.*auto-generate:[[:space:]]*//' | tr -d '"' | tr -d "'")

    # 提取日志配置 - 使用更精确的方法
    local log_level=$(grep -A 5 "logging:" "$yaml_file" | grep "level:" | head -1 | sed 's/.*level:[[:space:]]*//' | tr -d '"' | tr -d "'")
    local log_format=$(grep -A 5 "logging:" "$yaml_file" | grep "format:" | head -1 | sed 's/.*format:[[:space:]]*//' | tr -d '"' | tr -d "'")

    # 提取文件输出配置
    local log_file=$(grep -A 15 "outputs:" "$yaml_file" | grep "file:" | head -1 | sed 's/.*file:[[:space:]]*//' | tr -d '"' | tr -d "'")
    local max_size=$(grep -A 15 "outputs:" "$yaml_file" | grep "maxSize:" | head -1 | sed 's/.*maxSize:[[:space:]]*//')
    local max_age=$(grep -A 15 "outputs:" "$yaml_file" | grep "maxAge:" | head -1 | sed 's/.*maxAge:[[:space:]]*//')
    local max_backups=$(grep -A 15 "outputs:" "$yaml_file" | grep "maxBackups:" | head -1 | sed 's/.*maxBackups:[[:space:]]*//')
    local compress=$(grep -A 15 "outputs:" "$yaml_file" | grep "compress:" | head -1 | sed 's/.*compress:[[:space:]]*//')

    # 提取控制台输出配置
    local console_stderr=$(grep -A 20 "outputs:" "$yaml_file" | grep "stderr:" | head -1 | sed 's/.*stderr:[[:space:]]*//')

    # 提取服务器地址 - 精确匹配，避免混入其他数据
    local addrs=""
    local first_addr=true

    # 只提取 comms.addrs 部分的地址，避免混入日志配置
    local in_addrs=false
    while IFS= read -r line; do
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

        # 检测到 addrs: 开始
        if [[ "$line" =~ ^addrs:[[:space:]]*$ ]]; then
            in_addrs=true
            continue
        fi

        # 检测到下一个顶级配置项，停止解析
        if [[ "$line" =~ ^[a-zA-Z][a-zA-Z-]*:[[:space:]]*$ ]] && [ "$in_addrs" = true ]; then
            break
        fi

        # 在 addrs 部分且是列表项
        if [ "$in_addrs" = true ] && [[ "$line" =~ ^-[[:space:]] ]]; then
            local addr=$(echo "$line" | sed 's/^-[[:space:]]*//' | tr -d '"' | tr -d "'")
            # 验证地址格式，确保包含端口号
            if [[ "$addr" =~ ^[^:]+:[0-9]+$ ]]; then
                if [ "$first_addr" = false ]; then
                    addrs="${addrs},"
                fi
                addrs="${addrs}\"$addr\""
                first_addr=false
            fi
        fi
    done < "$yaml_file"

    # 设置默认值
    customer_id=${customer_id:-12345}
    client_id=${client_id:-223}
    tls_enabled=${tls_enabled:-false}
    tls_cert_dir=${tls_cert_dir:-./certs}
    tls_cert_file=${tls_cert_file:-server.crt}
    tls_key_file=${tls_key_file:-server.key}
    tls_skip_verify=${tls_skip_verify:-true}
    tls_auto_generate=${tls_auto_generate:-true}
    log_level=${log_level:-DEBUG}
    log_format=${log_format:-json}
    log_file=${log_file:-/var/log/agent.log}
    max_size=${max_size:-128}
    max_age=${max_age:-30}
    max_backups=${max_backups:-10}
    compress=${compress:-true}
    console_stderr=${console_stderr:-false}

    if [ -z "$addrs" ]; then
        addrs="\"comm.com:12345\",\"127.0.0.1:50051\""
    fi

    # 生成JSON
    echo "{"
    echo "  \"client\": {"
    echo "    \"customer-id\": $customer_id,"
    echo "    \"client-id\": $client_id"
    echo "  },"
    echo "  \"comms\": {"
    echo "    \"addrs\": [$addrs],"
    echo "    \"tls\": {"
    echo "      \"enabled\": $tls_enabled,"
    echo "      \"cert-dir\": \"$tls_cert_dir\","
    echo "      \"cert-file\": \"$tls_cert_file\","
    echo "      \"key-file\": \"$tls_key_file\","
    echo "      \"skip-verify\": $tls_skip_verify,"
    echo "      \"auto-generate\": $tls_auto_generate"
    echo "    }"
    echo "  },"
    echo "  \"logging\": {"
    echo "    \"level\": \"$log_level\","
    echo "    \"format\": \"$log_format\","
    echo "    \"outputs\": ["
    echo "      {"
    echo "        \"type\": \"file\","
    echo "        \"file\": \"$log_file\","
    echo "        \"maxSize\": $max_size,"
    echo "        \"maxAge\": $max_age,"
    echo "        \"maxBackups\": $max_backups,"
    echo "        \"compress\": $compress"
    echo "      },"
    echo "      {"
    echo "        \"type\": \"console\","
    echo "        \"stderr\": $console_stderr"
    echo "      }"
    echo "    ]"
    echo "  }"
    echo "}"
}

# 解析JSON配置数据并生成完整的YAML（包含TLS配置）
parse_json_to_yaml() {
    local json_data="$1"
    local output_file="$2"

    # 提取客户端配置
    customer_id=$(echo "$json_data" | grep -o '"customer-id":[[:space:]]*[0-9]*' | sed 's/"customer-id":[[:space:]]*//')
    client_id=$(echo "$json_data" | grep -o '"client-id":[[:space:]]*[0-9]*' | sed 's/"client-id":[[:space:]]*//')

    # 提取TLS配置
    tls_enabled=$(echo "$json_data" | grep -o '"enabled":[[:space:]]*[a-z]*' | sed 's/"enabled":[[:space:]]*//')
    tls_cert_dir=$(echo "$json_data" | grep -o '"cert-dir":[[:space:]]*"[^"]*"' | sed 's/"cert-dir":[[:space:]]*"//' | sed 's/"$//')
    tls_cert_file=$(echo "$json_data" | grep -o '"cert-file":[[:space:]]*"[^"]*"' | sed 's/"cert-file":[[:space:]]*"//' | sed 's/"$//')
    tls_key_file=$(echo "$json_data" | grep -o '"key-file":[[:space:]]*"[^"]*"' | sed 's/"key-file":[[:space:]]*"//' | sed 's/"$//')
    tls_skip_verify=$(echo "$json_data" | grep -o '"skip-verify":[[:space:]]*[a-z]*' | sed 's/"skip-verify":[[:space:]]*//')
    tls_auto_generate=$(echo "$json_data" | grep -o '"auto-generate":[[:space:]]*[a-z]*' | sed 's/"auto-generate":[[:space:]]*//')

    # 提取日志配置
    log_level=$(echo "$json_data" | grep -o '"level":[[:space:]]*"[^"]*"' | sed 's/"level":[[:space:]]*"//' | sed 's/"$//')
    log_format=$(echo "$json_data" | grep -o '"format":[[:space:]]*"[^"]*"' | sed 's/"format":[[:space:]]*"//' | sed 's/"$//')
    log_file=$(echo "$json_data" | grep -o '"file":[[:space:]]*"[^"]*"' | sed 's/"file":[[:space:]]*"//' | sed 's/"$//')
    max_size=$(echo "$json_data" | grep -o '"maxSize":[[:space:]]*[0-9]*' | sed 's/"maxSize":[[:space:]]*//')
    max_age=$(echo "$json_data" | grep -o '"maxAge":[[:space:]]*[0-9]*' | sed 's/"maxAge":[[:space:]]*//')
    max_backups=$(echo "$json_data" | grep -o '"maxBackups":[[:space:]]*[0-9]*' | sed 's/"maxBackups":[[:space:]]*//')
    compress=$(echo "$json_data" | grep -o '"compress":[[:space:]]*[a-z]*' | sed 's/"compress":[[:space:]]*//')
    console_stderr=$(echo "$json_data" | grep -o '"stderr":[[:space:]]*[a-z]*' | sed 's/"stderr":[[:space:]]*//')

    # 提取服务器地址数组
    local addrs_section=$(echo "$json_data" | grep -o '"addrs":[[:space:]]*\[[^]]*\]')
    local addrs=""
    if [ -n "$addrs_section" ]; then
        # 提取地址列表
        local addr_list=$(echo "$addrs_section" | sed 's/"addrs":[[:space:]]*\[//' | sed 's/\]$//' | tr ',' '\n')
        while IFS= read -r addr; do
            addr=$(echo "$addr" | sed 's/^[[:space:]]*"//' | sed 's/"[[:space:]]*$//')
            if [ -n "$addr" ]; then
                addrs="${addrs}    - \"$addr\"\n"
            fi
        done <<< "$addr_list"
    fi

    # 设置默认值
    customer_id=${customer_id:-12345}
    client_id=${client_id:-223}
    tls_enabled=${tls_enabled:-false}
    tls_cert_dir=${tls_cert_dir:-./certs}
    tls_cert_file=${tls_cert_file:-server.crt}
    tls_key_file=${tls_key_file:-server.key}
    tls_skip_verify=${tls_skip_verify:-true}
    tls_auto_generate=${tls_auto_generate:-true}
    log_level=${log_level:-DEBUG}
    log_format=${log_format:-json}
    log_file=${log_file:-/var/log/agent.log}
    max_size=${max_size:-128}
    max_age=${max_age:-30}
    max_backups=${max_backups:-10}
    compress=${compress:-true}
    console_stderr=${console_stderr:-false}

    if [ -z "$addrs" ]; then
        addrs="    - \"comm.com:12345\"\n    - \"127.0.0.1:50051\""
    fi

    # 生成完整的YAML文件
    cat > "$output_file" << EOF
client:
  customer-id: $customer_id
  client-id: $client_id

comms:
  addrs:
$(echo -e "$addrs")
  tls:
    enabled: $tls_enabled
    cert-dir: "$tls_cert_dir"
    cert-file: "$tls_cert_file"
    key-file: "$tls_key_file"
    skip-verify: $tls_skip_verify
    auto-generate: $tls_auto_generate

logging:
  level: "$log_level"
  format: "$log_format"
  outputs:
    - type: "file"
      file: "$log_file"
      maxSize: $max_size
      maxAge: $max_age
      maxBackups: $max_backups
      compress: $compress
    - type: "console"
      stderr: $console_stderr
EOF
}

# 处理GET请求 - 读取配置
handle_get() {
    yaml_to_json "$CONFIG_FILE"
}

# 处理POST请求 - 保存配置
handle_post() {
    # 简化的权限检查，避免HTML输出
    if [ -z "$HTTP_COOKIE" ]; then
        error_response "未授权访问" 401
    fi
    
    # 读取POST数据
    local content_length="${CONTENT_LENGTH:-0}"
    if [ "$content_length" -eq 0 ]; then
        error_response "没有接收到数据" 400
    fi

    # 读取请求体
    local post_data
    post_data=$(head -c "$content_length")

    # 检查配置目录
    local config_dir
    config_dir=$(dirname "$CONFIG_FILE")
    if [ ! -d "$config_dir" ]; then
        mkdir -p "$config_dir"
    fi

    # 解析JSON并生成YAML
    parse_json_to_yaml "$post_data" "$CONFIG_FILE.tmp"

    # 简单验证YAML文件格式（检查基本结构）
    if ! grep -q "^client:" "$CONFIG_FILE.tmp" || \
       ! grep -q "^comms:" "$CONFIG_FILE.tmp" || \
       ! grep -q "^logging:" "$CONFIG_FILE.tmp"; then
        rm -f "$CONFIG_FILE.tmp"
        error_response "配置文件格式验证失败" 400
    fi

    # 备份原配置文件
    if [ -f "$CONFIG_FILE" ]; then
        cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    fi

    # 移动临时文件到正式位置
    mv "$CONFIG_FILE.tmp" "$CONFIG_FILE"

    # 设置文件权限
    chmod 644 "$CONFIG_FILE"

    success_response "配置保存成功"
}

# 主处理逻辑
main() {
    # 根据请求方法处理
    case "$REQUEST_METHOD" in
        "GET")
            handle_get
            ;;
        "POST")
            handle_post
            ;;
        *)
            error_response "不支持的请求方法: $REQUEST_METHOD" 405
            ;;
    esac
}

# 执行主函数
main
