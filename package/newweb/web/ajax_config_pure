#!/bin/sh
# 纯 JSON API 版本 - 完全避免 CGI 框架污染

# 立即输出 HTTP 头部
printf "Content-Type: application/json\r\n"
printf "Cache-Control: no-cache\r\n"
printf "Access-Control-Allow-Origin: *\r\n"
printf "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n"
printf "Access-Control-Allow-Headers: Content-Type\r\n"
printf "\r\n"

# 处理 OPTIONS 请求（CORS 预检）
if [ "$REQUEST_METHOD" = "OPTIONS" ]; then
    exit 0
fi

# 配置文件路径
PGPATH="${PGPATH:-/usr/panabit}"
CONFIG_FILE="${PGPATH}/app/unisase_agent/config/config.yaml"

# 错误响应函数
error_response() {
    echo "{\"error\": \"$1\", \"code\": ${2:-500}}"
    exit 1
}

# 成功响应函数
success_response() {
    echo "{\"success\": true, \"message\": \"$1\"}"
}

# 精确的 YAML 转 JSON 函数
yaml_to_json_precise() {
    local yaml_file="$1"

    if [ ! -f "$yaml_file" ]; then
        error_response "配置文件不存在" 404
    fi

    # 提取客户端配置
    local customer_id=$(awk '/^client:/{flag=1; next} /^[a-z]/{flag=0} flag && /customer-id:/{print $2}' "$yaml_file")
    local client_id=$(awk '/^client:/{flag=1; next} /^[a-z]/{flag=0} flag && /client-id:/{print $2}' "$yaml_file")

    # 提取服务器地址 - 只从 comms.addrs 部分提取
    local addrs=""
    local first_addr=true
    while IFS= read -r line; do
        if [ -n "$line" ]; then
            # 验证是有效的地址格式 (host:port)
            if echo "$line" | grep -q "^[^:]\+:[0-9]\+$"; then
                if [ "$first_addr" = false ]; then
                    addrs="${addrs},"
                fi
                addrs="${addrs}\"$line\""
                first_addr=false
            fi
        fi
    done <<< "$(awk '/^comms:/{flag=1; next} /^[a-z]/{flag=0} flag && /addrs:/{flag2=1; next} flag2 && /^[[:space:]]*-/{gsub(/^[[:space:]]*-[[:space:]]*/, ""); gsub(/["'"'"']/, ""); print}' "$yaml_file")"

    # 提取 TLS 配置
    local tls_enabled=$(awk '/^comms:/{flag=1; next} /^[a-z]/{flag=0} flag && /tls:/{flag2=1; next} flag2 && /enabled:/{print $2; exit}' "$yaml_file")
    local tls_cert_dir=$(awk '/^comms:/{flag=1; next} /^[a-z]/{flag=0} flag && /tls:/{flag2=1; next} flag2 && /cert-dir:/{gsub(/["'"'"']/, "", $2); print $2; exit}' "$yaml_file")
    local tls_cert_file=$(awk '/^comms:/{flag=1; next} /^[a-z]/{flag=0} flag && /tls:/{flag2=1; next} flag2 && /cert-file:/{gsub(/["'"'"']/, "", $2); print $2; exit}' "$yaml_file")
    local tls_key_file=$(awk '/^comms:/{flag=1; next} /^[a-z]/{flag=0} flag && /tls:/{flag2=1; next} flag2 && /key-file:/{gsub(/["'"'"']/, "", $2); print $2; exit}' "$yaml_file")
    local tls_skip_verify=$(awk '/^comms:/{flag=1; next} /^[a-z]/{flag=0} flag && /tls:/{flag2=1; next} flag2 && /skip-verify:/{print $2; exit}' "$yaml_file")
    local tls_auto_generate=$(awk '/^comms:/{flag=1; next} /^[a-z]/{flag=0} flag && /tls:/{flag2=1; next} flag2 && /auto-generate:/{print $2; exit}' "$yaml_file")

    # 提取日志配置
    local log_level=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /level:/{gsub(/["'"'"']/, "", $2); print $2; exit}' "$yaml_file")
    local log_format=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /format:/{gsub(/["'"'"']/, "", $2); print $2; exit}' "$yaml_file")

    # 提取文件输出配置
    local log_file=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /outputs:/{flag2=1; next} flag2 && /type: "file"/{flag3=1; next} flag3 && /file:/{gsub(/["'"'"']/, "", $2); print $2; exit}' "$yaml_file")
    local max_size=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /outputs:/{flag2=1; next} flag2 && /type: "file"/{flag3=1; next} flag3 && /maxSize:/{print $2; exit}' "$yaml_file")
    local max_age=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /outputs:/{flag2=1; next} flag2 && /type: "file"/{flag3=1; next} flag3 && /maxAge:/{print $2; exit}' "$yaml_file")
    local max_backups=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /outputs:/{flag2=1; next} flag2 && /type: "file"/{flag3=1; next} flag3 && /maxBackups:/{print $2; exit}' "$yaml_file")
    local compress=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /outputs:/{flag2=1; next} flag2 && /type: "file"/{flag3=1; next} flag3 && /compress:/{print $2; exit}' "$yaml_file")

    # 提取控制台输出配置
    local console_stderr=$(awk '/^logging:/{flag=1; next} /^[a-z]/{flag=0} flag && /outputs:/{flag2=1; next} flag2 && /type: "console"/{flag3=1; next} flag3 && /stderr:/{print $2; exit}' "$yaml_file")

    # 设置默认值
    customer_id=${customer_id:-12345}
    client_id=${client_id:-223}
    tls_enabled=${tls_enabled:-false}
    tls_cert_dir=${tls_cert_dir:-./certs}
    tls_cert_file=${tls_cert_file:-server.crt}
    tls_key_file=${tls_key_file:-server.key}
    tls_skip_verify=${tls_skip_verify:-true}
    tls_auto_generate=${tls_auto_generate:-true}
    log_level=${log_level:-DEBUG}
    log_format=${log_format:-json}
    log_file=${log_file:-/var/log/agent.log}
    max_size=${max_size:-128}
    max_age=${max_age:-30}
    max_backups=${max_backups:-10}
    compress=${compress:-true}
    console_stderr=${console_stderr:-false}

    if [ -z "$addrs" ]; then
        addrs="\"comm.com:12345\",\"127.0.0.1:50051\""
    fi

    # 生成纯净的 JSON
    cat << EOF
{
  "client": {
    "customer-id": $customer_id,
    "client-id": $client_id
  },
  "comms": {
    "addrs": [$addrs],
    "tls": {
      "enabled": $tls_enabled,
      "cert-dir": "$tls_cert_dir",
      "cert-file": "$tls_cert_file",
      "key-file": "$tls_key_file",
      "skip-verify": $tls_skip_verify,
      "auto-generate": $tls_auto_generate
    }
  },
  "logging": {
    "level": "$log_level",
    "format": "$log_format",
    "outputs": [
      {
        "type": "file",
        "file": "$log_file",
        "maxSize": $max_size,
        "maxAge": $max_age,
        "maxBackups": $max_backups,
        "compress": $compress
      },
      {
        "type": "console",
        "stderr": $console_stderr
      }
    ]
  }
}
EOF
}

# 简化的 JSON 转 YAML 函数
json_to_yaml_simple() {
    local json_data="$1"
    local output_file="$2"

    # 提取基本字段
    local customer_id=$(echo "$json_data" | grep -o '"customer-id":[[:space:]]*[0-9]*' | sed 's/"customer-id":[[:space:]]*//')
    local client_id=$(echo "$json_data" | grep -o '"client-id":[[:space:]]*[0-9]*' | sed 's/"client-id":[[:space:]]*//')
    local log_level=$(echo "$json_data" | grep -o '"level":[[:space:]]*"[^"]*"' | sed 's/"level":[[:space:]]*"//' | sed 's/"$//')
    local log_format=$(echo "$json_data" | grep -o '"format":[[:space:]]*"[^"]*"' | sed 's/"format":[[:space:]]*"//' | sed 's/"$//')

    # 设置默认值
    customer_id=${customer_id:-12345}
    client_id=${client_id:-223}
    log_level=${log_level:-DEBUG}
    log_format=${log_format:-json}

    # 生成 YAML 文件
    cat > "$output_file" << EOF
client:
  customer-id: $customer_id
  client-id: $client_id

comms:
  addrs:
    - "comm.com:12345"
    - "127.0.0.1:50051"
  tls:
    enabled: false
    cert-dir: "./certs"
    cert-file: "server.crt"
    key-file: "server.key"
    skip-verify: true
    auto-generate: true

logging:
  level: "$log_level"
  format: "$log_format"
  outputs:
    - type: "file"
      file: "/var/log/agent.log"
      maxSize: 128
      maxAge: 30
      maxBackups: 10
      compress: true
    - type: "console"
      stderr: false
EOF
}

# 处理 GET 请求
handle_get() {
    yaml_to_json_precise "$CONFIG_FILE"
}

# 处理 POST 请求
handle_post() {
    local content_length="${CONTENT_LENGTH:-0}"
    if [ "$content_length" -eq 0 ]; then
        error_response "没有接收到数据" 400
    fi

    local post_data
    post_data=$(head -c "$content_length")

    local config_dir
    config_dir=$(dirname "$CONFIG_FILE")
    if [ ! -d "$config_dir" ]; then
        mkdir -p "$config_dir"
    fi

    json_to_yaml_simple "$post_data" "$CONFIG_FILE.tmp"

    if [ -f "$CONFIG_FILE" ]; then
        cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    fi

    mv "$CONFIG_FILE.tmp" "$CONFIG_FILE"
    chmod 644 "$CONFIG_FILE"

    success_response "配置保存成功"
}

# 主处理逻辑
case "$REQUEST_METHOD" in
    "GET")
        handle_get
        ;;
    "POST")
        handle_post
        ;;
    *)
        error_response "不支持的请求方法: $REQUEST_METHOD" 405
        ;;
esac
